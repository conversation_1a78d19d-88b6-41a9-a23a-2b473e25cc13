"use client";

import { useState } from "react";
import { useUser } from "../../context/UserContext";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function Login() {
  const { setUser } = useUser();
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const handleLogin = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // التحقق من الحقول
    if (!email || !password) {
      setError("يرجى إدخال البريد الإلكتروني وكلمة المرور");
      return;
    }

    // تحقق وهمي - استبدله بـ API حقيقية لاحقًا
    if (email.includes("@") && password.length > 3) {
      const loggedInUser = {
        email,
        name: email.split("@")[0],
        photoURL: "/avatar.png",
      };

      // ✅ حفظ الاسم في localStorage
      localStorage.setItem("username", loggedInUser.name);

      // حفظ المستخدم في الـ context
      setUser(loggedInUser);

      // التوجيه إلى الصفحة الرئيسية
      router.push("/");
    } else {
      setError("بيانات الدخول غير صحيحة");
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded shadow">
      <h1 className="text-2xl mb-6 font-semibold text-center">تسجيل الدخول</h1>

      {error && <p className="text-red-500 mb-4 text-center">{error}</p>}

      <form onSubmit={handleLogin} className="space-y-4">
        <input
          type="email"
          placeholder="البريد الإلكتروني"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full border px-3 py-2 rounded focus:outline-none focus:ring focus:border-orange-500"
        />
        <input
          type="password"
          placeholder="كلمة المرور"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full border px-3 py-2 rounded focus:outline-none focus:ring focus:border-orange-500"
        />
        <button
          type="submit"
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded font-semibold transition"
        >
          دخول
        </button>
      </form>

      <p className="mt-6 text-center text-sm text-gray-600">
        ليس لديك حساب؟{" "}
        <Link href="/signup" className="text-blue-500 hover:underline">
          أنشئ حسابًا
        </Link>
      </p>
    </div>
  );
}
