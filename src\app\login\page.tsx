'use client'; // مهم علشان نقدر نستخدم useState في Next.js App Router

import { useState } from 'react';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [msg, setMsg] = useState('');

  const handleLogin = async () => {
    try {
      const response = await fetch('http://localhost:3000/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) throw new Error(data.message || 'Login failed');

      // ✅ تسجيل الدخول ناجح
      setMsg('تم تسجيل الدخول بنجاح!');
      localStorage.setItem('token', data.token); // نحفظ التوكن

    } catch (err) {
      setMsg(`خطأ: ${err.message}`);
    }
  };

  return (
    <div className="max-w-sm mx-auto mt-10">
      <h2 className="text-lg font-bold mb-4">تسجيل الدخول</h2>

      <input
        type="email"
        placeholder="البريد الإلكتروني"
        className="border p-2 w-full mb-2"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />

      <input
        type="password"
        placeholder="كلمة المرور"
        className="border p-2 w-full mb-2"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />

      <button
        onClick={handleLogin}
        className="bg-blue-600 text-white px-4 py-2 rounded w-full hover:bg-blue-700"
      >
        دخول
      </button>

      {msg && <p className="mt-3 text-sm text-red-500">{msg}</p>}
    </div>
  );
}
