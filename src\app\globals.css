/* globals.css */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ممكن تضيف هنا تنسيقات إضافية */





:root {
  --background: #ffffff;
  --foreground: #000000;

  /* خطوط مخصصة (إذا كنت معرفها في tailwind.config.js) */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Tailwind CSS Utility Classes */
.w-full { width: 100%; }
.max-w-7xl { max-width: 80rem; }
.max-w-md { max-width: 28rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.flex { display: flex; }
.flex-1 { flex: 1 1 0%; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.bg-white { background-color: #ffffff; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-500 { background-color: #60a5fa; }
.bg-blue-600 { background-color: #3b82f6; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.text-black { color: #000000; }
.text-white { color: #ffffff; }

.text-gray-800 { color: #1f2937; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-gray-300 { color: #d1d5db; }
.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-red-500 { color: #ef4444; }
.text-yellow-600 { color: #d97706; }
.hover\:text-blue-600:hover { color: #2962dd; }
.hover\:text-yellow-600:hover { color: #d97706; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-20 { margin-top: 5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.border { border-width: 1px; border-style: solid; border-color: #d1d5db; }
.border-blue-500 { border-color: #3b82f6; }
.border-gray-300 { border-color: #d1d5db; }
.border-white { border-color: #ffffff; }
.rounded { border-radius: 0.25rem; }
.rounded-full { border-radius: 9999px; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.cursor-pointer { cursor: pointer; }
.overflow-hidden { overflow: hidden; }
.relative { position: relative; }
.absolute { position: absolute; }
.right-3 { right: 0.75rem; }
.right-14 { right: 3.5rem; }
.top-2\.5 { top: 0.625rem; }
.top-12 { top: 3rem; }
.w-40 { width: 10rem; }
.w-30 { width: 7.5rem; }
.w-36 { width: 9rem; }
.h-20 { height: 5rem; }
.h-36 { height: 9rem; }
.z-50 { z-index: 50; }
.block { display: block; }
.whitespace-nowrap { white-space: nowrap; }
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.hover\:underline:hover { text-decoration-line: underline; }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-400:focus { --tw-ring-color: #60a5fa; }
.focus\:border-orange-500:focus { border-color: #f97316; }
.animate-bounce { animation: bounce 1s infinite; }
/* Animations */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-marquee {
  display: inline-block;
  animation: marquee 12s linear infinite;
}
a {
  text-decoration: none;
}

/* تنسيق أزرار الـ Login */
a.inline-block {
  text-decoration: none !important;
}

a.inline-block:hover {
  text-decoration: none !important;
}

/* تنسيق عام للروابط */
a:hover {
  text-decoration: none !important;
}
