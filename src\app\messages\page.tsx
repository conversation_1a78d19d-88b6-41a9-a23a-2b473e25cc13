'use client';

import { useState } from 'react';
import { useUser } from '@/context/UserContext';
import { FaEnvelope, FaUser, FaPaperPlane, FaSearch } from 'react-icons/fa';

export default function MessagesPage() {
  const { user } = useUser();
  const [selectedChat, setSelectedChat] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  const mockChats = [
    {
      id: 1,
      name: 'دعم العملاء',
      lastMessage: 'مرحباً، كيف يمكنني مساعدتك؟',
      time: '10:30 AM',
      unread: 2,
      avatar: '🎧'
    },
    {
      id: 2,
      name: 'أحمد محمد',
      lastMessage: 'شكراً لك على الخدمة الممتازة',
      time: '9:15 AM',
      unread: 0,
      avatar: '👤'
    },
    {
      id: 3,
      name: 'فريق المبيعات',
      lastMessage: 'لديك عرض خاص جديد!',
      time: 'أمس',
      unread: 1,
      avatar: '💼'
    }
  ];

  const mockMessages = {
    1: [
      { id: 1, sender: 'support', text: 'مرحباً! كيف يمكنني مساعدتك اليوم؟', time: '10:00 AM' },
      { id: 2, sender: 'user', text: 'أريد الاستفسار عن طلبي الأخير', time: '10:05 AM' },
      { id: 3, sender: 'support', text: 'بالطبع، يمكنني مساعدتك. ما هو رقم الطلب؟', time: '10:30 AM' }
    ],
    2: [
      { id: 1, sender: 'user', text: 'مرحباً أحمد', time: '9:00 AM' },
      { id: 2, sender: 'other', text: 'أهلاً وسهلاً', time: '9:10 AM' },
      { id: 3, sender: 'other', text: 'شكراً لك على الخدمة الممتازة', time: '9:15 AM' }
    ],
    3: [
      { id: 1, sender: 'other', text: 'لديك عرض خاص جديد!', time: 'أمس' },
      { id: 2, sender: 'other', text: 'خصم 20% على جميع الألعاب', time: 'أمس' }
    ]
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim() && selectedChat) {
      // Here you would typically send the message to your backend
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  const filteredChats = mockChats.filter(chat =>
    chat.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">يرجى تسجيل الدخول</h2>
          <p className="text-gray-600">تحتاج إلى تسجيل الدخول لعرض رسائلك</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FaEnvelope className="text-orange-500" />
            Messages
          </h1>
        </div>

        {/* Messages Container */}
        <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: '600px' }}>
          <div className="flex h-full">
            {/* Chat List */}
            <div className="w-1/3 border-r border-gray-200 flex flex-col">
              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في المحادثات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>

              {/* Chat List */}
              <div className="flex-1 overflow-y-auto">
                {filteredChats.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => setSelectedChat(chat)}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition ${
                      selectedChat?.id === chat.id ? 'bg-orange-50 border-orange-200' : ''
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center text-xl">
                        {chat.avatar}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-900 truncate">{chat.name}</h3>
                          <span className="text-xs text-gray-500">{chat.time}</span>
                        </div>
                        <p className="text-sm text-gray-600 truncate">{chat.lastMessage}</p>
                      </div>
                      {chat.unread > 0 && (
                        <div className="w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center">
                          {chat.unread}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {selectedChat ? (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                        {selectedChat.avatar}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{selectedChat.name}</h3>
                        <p className="text-sm text-green-600">متصل الآن</p>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {mockMessages[selectedChat.id]?.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.sender === 'user'
                              ? 'bg-orange-500 text-white'
                              : 'bg-gray-200 text-gray-900'
                          }`}
                        >
                          <p className="text-sm">{message.text}</p>
                          <p className={`text-xs mt-1 ${
                            message.sender === 'user' ? 'text-orange-100' : 'text-gray-500'
                          }`}>
                            {message.time}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t border-gray-200">
                    <form onSubmit={handleSendMessage} className="flex gap-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="اكتب رسالتك..."
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                      />
                      <button
                        type="submit"
                        disabled={!newMessage.trim()}
                        className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FaPaperPlane />
                      </button>
                    </form>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <FaEnvelope className="text-gray-300 text-6xl mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">اختر محادثة</h3>
                    <p className="text-gray-600">اختر محادثة من القائمة لبدء المراسلة</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
