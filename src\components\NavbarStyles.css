/* تنسيق مخصص للناف بار */
.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
}

/* تنسيق الروابط مع hover أزرق */
.nav-link-custom {
  color: #333 !important;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px !important;
  border-radius: 6px;
}

.nav-link-custom:hover {
  color: #007bff !important;
  background-color: rgba(0, 123, 255, 0.1) !important;
  transform: translateY(-1px);
}

/* تنسيق الـ hamburger menu */
.navbar-toggler,
.custom-toggle {
  border: none !important;
  padding: 4px 8px !important;
  margin-left: auto !important;
  order: 2 !important;
  box-shadow: none !important;
  outline: none !important;
}

.navbar-toggler:focus,
.custom-toggle:focus {
  box-shadow: none !important;
  outline: none !important;
  border: none !important;
}

/* تنسيق الخطوط الثلاثة */
.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

