'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/context/UserContext';
import { orderAPI } from '@/services/api';
import { FaList, FaGamepad, FaCalendar, FaDollarSign, FaEye } from 'react-icons/fa';

export default function MyListingsPage() {
  const { user } = useUser();
  const [orders, setOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (user) {
      fetchOrders();
    }
  }, [user]);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      const data = await orderAPI.getOrders();
      setOrders(data.orders || []);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setMessage('خطأ في تحميل الطلبات');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'قيد الانتظار';
      case 'cancelled':
        return 'ملغي';
      default:
        return status || 'غير محدد';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">يرجى تسجيل الدخول</h2>
          <p className="text-gray-600">تحتاج إلى تسجيل الدخول لعرض طلباتك</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FaList className="text-purple-500" />
            My Listings
          </h1>
          <p className="text-gray-600 mt-2">جميع طلباتك ومشترياتك</p>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">جاري تحميل الطلبات...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <FaList className="text-gray-300 text-6xl mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد طلبات</h3>
              <p className="text-gray-600 mb-6">لم تقم بأي طلبات حتى الآن</p>
              <button className="bg-purple-500 text-white px-6 py-2 rounded-md hover:bg-purple-600 transition">
                تصفح المنتجات
              </button>
            </div>
          ) : (
            <div className="overflow-hidden">
              {/* Table Header */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm font-medium text-gray-700">
                  <div className="flex items-center gap-2">
                    <FaGamepad />
                    اللعبة
                  </div>
                  <div className="flex items-center gap-2">
                    <FaDollarSign />
                    المبلغ
                  </div>
                  <div className="flex items-center gap-2">
                    <FaCalendar />
                    التاريخ
                  </div>
                  <div>الحالة</div>
                  <div>الإجراءات</div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-200">
                {orders.map((order, index) => (
                  <div key={order._id || index} className="px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                      {/* Game */}
                      <div>
                        <p className="font-medium text-gray-900">{order.game || 'غير محدد'}</p>
                        <p className="text-sm text-gray-600">{order.type || 'نوع غير محدد'}</p>
                        {order.packageType && (
                          <p className="text-xs text-purple-600">{order.packageType}</p>
                        )}
                      </div>

                      {/* Amount */}
                      <div>
                        <p className="font-semibold text-gray-900">${order.amount || 0}</p>
                        <p className="text-sm text-gray-600">{order.paymentMethod || 'غير محدد'}</p>
                      </div>

                      {/* Date */}
                      <div>
                        <p className="text-sm text-gray-900">
                          {order.createdAt 
                            ? new Date(order.createdAt).toLocaleDateString('ar-EG')
                            : 'تاريخ غير محدد'
                          }
                        </p>
                      </div>

                      {/* Status */}
                      <div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {getStatusText(order.status)}
                        </span>
                      </div>

                      {/* Actions */}
                      <div>
                        <button className="flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm">
                          <FaEye />
                          عرض
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Message */}
        {message && (
          <div className="mt-4 p-3 rounded-md text-sm bg-red-100 text-red-700">
            {message}
          </div>
        )}
      </div>
    </div>
  );
}
