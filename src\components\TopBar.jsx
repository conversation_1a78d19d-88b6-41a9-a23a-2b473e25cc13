'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
// Using emoji instead of react-icons for better compatibility
import Select from 'react-select';
import { useUser } from '@/context/UserContext';
import { authAPI } from '@/services/api';

const countries = [
  { name: 'Egypt', code: 'eg' },
  { name: 'Saudi Arabia', code: 'sa' },
  { name: 'UAE', code: 'ae' },
  { name: 'France', code: 'fr' },
  { name: 'Germany', code: 'de' },
  { name: 'Italy', code: 'it' },
  { name: 'Spain', code: 'es' },
  { name: 'UK', code: 'gb' },
  { name: 'Jordan', code: 'jo' },
  { name: 'Iraq', code: 'iq' },
  { name: 'Algeria', code: 'dz' },
  { name: 'Morocco', code: 'ma' },
];

const countryOptions = countries.map((c) => ({
  value: c.code,
  label: c.name,
  flag: `https://flagcdn.com/w40/${c.code.toLowerCase()}.png`,
}));

export default function TopBar() {
  const [mounted, setMounted] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countryOptions[0]);
  const [showCart, setShowCart] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { user, setUser } = useUser();
  const router = useRouter();

  useEffect(() => {
    setMounted(true);

    // Check if user is already logged in
    if (authAPI.isAuthenticated()) {
      const userData = authAPI.getCurrentUser();
      if (userData && !user) {
        setUser(userData);
      }
    }
  }, [user, setUser]);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showUserMenu || showCart) {
        setShowUserMenu(false);
        setShowCart(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu, showCart]);

  if (!mounted) return null;

  const handleLogout = () => {
    authAPI.logout();
    setUser(null);
    setShowUserMenu(false);
    router.push('/login');
  };

  return (
<div className="relative w-full bg-white shadow-sm topbar-container">

      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-2 sm:py-3 flex items-center justify-between gap-2 sm:gap-4 overflow-hidden">
        {/* Logo */}
        <div className="flex-shrink-0">
          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm sm:text-lg">
            B1
          </div>
        </div>

        {/* Search */}
        <div className="flex-1 px-2 sm:px-6 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
          <input
            type="text"
            placeholder="Search..."
            className="w-full px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2 sm:gap-4 relative flex-shrink-0">
          {/* Country Selector */}
          <div className="w-20 sm:w-[130px] border-none">
            <Select
              options={countryOptions}
              value={selectedCountry}
              onChange={(val) => setSelectedCountry(val)}
              isSearchable={false}
              className="text-xs sm:text-sm"
              formatOptionLabel={(option) => (
                <div className="flex items-center gap-1 sm:gap-2">
                  <img
                    src={option.flag}
                    alt={option.label}
                    style={{
                      width: '16px',
                      height: '12px',
                      objectFit: 'cover',
                      borderRadius: '2px',
                    }}
                    className="sm:w-5 sm:h-4"
                  />
                  <span className="text-xs hidden sm:inline">{option.label}</span>
                </div>
              )}
                styles={{
             control: (base) => ({
  ...base,
  borderRadius: '999px',
  paddingLeft: '4px',
  minHeight: '28px',
  boxShadow: 'none',
  border: 'none',
  fontSize: '12px',
}),

                indicatorsContainer: (base) => ({
                  ...base,
                  paddingRight: '4px',
                }),

                menu: (base) => ({
                  ...base,
                  zIndex: 999999,
                }),

                menuPortal: (base) => ({
                  ...base,
                  zIndex: 999999,
                }),
              }}
              menuPortalTarget={typeof document !== 'undefined' ? document.body : null}
            />
          </div>

          {/* Cart Icon */}
          <div className="relative">
            <button
              onClick={() => setShowCart(!showCart)}
              className="text-gray-500 hover:text-blue-600 text-lg sm:text-xl relative bg-transparent border-none outline-none p-2 rounded-full hover:bg-gray-100 transition"
            >
              🛒
              {/* Cart Badge */}
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                2
              </span>
            </button>

            {/* Mini Cart Dropdown */}
            {showCart && (
              <div className="dropdown-menu dropdown-content cart-dropdown">
                <div className="p-4">
                  <h3 className="text-sm font-semibold mb-3 text-gray-800 flex items-center gap-2">
                    🛒 Shopping Cart
                  </h3>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center mb-3 p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium text-gray-800">🎮 PUBG Mobile UC</p>
                        <p className="text-xs text-gray-500">Qty: 1 × 60 UC</p>
                      </div>
                      <span className="text-sm font-semibold text-gray-800">$5.00</span>
                    </div>
                    <div className="flex justify-between items-center mb-3 p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium text-gray-800">💎 Free Fire Diamonds</p>
                        <p className="text-xs text-gray-500">Qty: 1 × 100 Diamonds</p>
                      </div>
                      <span className="text-sm font-semibold text-gray-800">$3.00</span>
                    </div>
                  </div>
                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between items-center mb-3">
                      <span className="font-semibold text-gray-800">Total:</span>
                      <span className="font-bold text-lg text-blue-600">$8.00</span>
                    </div>
                    <Link
                      href="/cart"
                      className="block text-center bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition font-medium"
                      onClick={() => setShowCart(false)}
                    >
                      🛒 View Cart
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* User Authentication */}
          {user ? (
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 bg-green-500 text-white text-xs sm:text-sm font-medium px-3 sm:px-4 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-green-600 transition-all duration-300"
              >
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-white rounded-full flex items-center justify-center text-lg">
                  👤
                </div>
                <span className="hidden sm:inline">{user.userName || user.name || 'User'}</span>
              </button>

              {showUserMenu && (
                <div className="dropdown-menu dropdown-content">
                  <div>
                    {/* User Info Header */}
                    <div className="border-b pb-3 mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-xl">
                          👤
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800">{user.userName || user.name}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="space-y-1">
                      <Link
                        href="/profile"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="text-blue-500">👤</span>
                        My Profile
                      </Link>

                      <Link
                        href="/my-listings"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="text-purple-500">📋</span>
                        My Listing
                      </Link>

                      <Link
                        href="/wallet"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="text-green-500">💰</span>
                        My Wallet
                      </Link>

                      <Link
                        href="/messages"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="text-orange-500">💬</span>
                        Messages
                      </Link>

                      <Link
                        href="/settings"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="text-gray-500">⚙️</span>
                        Settings
                      </Link>

                      {/* Divider */}
                      <div className="border-t my-2"></div>

                     <Link
                href="/login"
                className="inline-block bg-blue-500 text-white text-xs sm:text-sm font-medium px-3 sm:px-6 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-blue-600 hover:shadow-md transition-all duration-300 whitespace-nowrap border-0"
                style={{
                  textDecoration: 'none',
                  outline: 'none',
                  boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'
                }}
              >
               log out
              </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Link
                href="/signup"
                className="inline-block bg-gray-500 text-white text-xs sm:text-sm font-medium px-2 sm:px-4 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-gray-600 transition-all duration-300 whitespace-nowrap border-0"
                style={{ textDecoration: 'none' }}
              >
                📝 Sign Up
              </Link>
<button
  onClick={handleLogout}
  className="inline-block bg-blue-500 text-white text-xs sm:text-sm font-medium px-3 sm:px-6 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-blue-600 hover:shadow-md transition-all duration-300 whitespace-nowrap border-0"
  style={{
    textDecoration: 'none',
    outline: 'none',
    boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'
  }}
>
  🔓 Log out
</button>

            </div>
          )}

        </div>
      </div>
    </div>
  );
}


