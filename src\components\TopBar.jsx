'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaShoppingCart, FaUser, FaSignOutAlt, FaUserCircle, FaList, FaWallet, FaEnvelope, FaCog } from 'react-icons/fa';
import Select from 'react-select';
import { useUser } from '@/context/UserContext';
import { authAPI } from '@/services/api';

const countries = [
  { name: 'Egypt', code: 'eg' },
  { name: 'Saudi Arabia', code: 'sa' },
  { name: 'UAE', code: 'ae' },
  { name: 'France', code: 'fr' },
  { name: 'Germany', code: 'de' },
  { name: 'Italy', code: 'it' },
  { name: 'Spain', code: 'es' },
  { name: 'UK', code: 'gb' },
  { name: 'Jordan', code: 'jo' },
  { name: 'Iraq', code: 'iq' },
  { name: 'Algeria', code: 'dz' },
  { name: 'Morocco', code: 'ma' },
];

const countryOptions = countries.map((c) => ({
  value: c.code,
  label: c.name,
  flag: `https://flagcdn.com/w40/${c.code.toLowerCase()}.png`,
}));

export default function TopBar() {
  const [mounted, setMounted] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countryOptions[0]);
  const [showCart, setShowCart] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { user, setUser } = useUser();

  useEffect(() => {
    setMounted(true);

    // Check if user is already logged in
    if (authAPI.isAuthenticated()) {
      const userData = authAPI.getCurrentUser();
      if (userData && !user) {
        setUser(userData);
      }
    }
  }, [user, setUser]);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showUserMenu || showCart) {
        setShowUserMenu(false);
        setShowCart(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu, showCart]);

  if (!mounted) return null;

  const handleLogout = () => {
    authAPI.logout();
    setUser(null);
    setShowUserMenu(false);
  };

  return (
<div className="relative w-full bg-white shadow-sm">

      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-2 sm:py-3 flex items-center justify-between gap-2 sm:gap-4 overflow-hidden">
        {/* Logo */}
        <div className="flex-shrink-0">
          <Image src="/logo.png" alt="Logo" width={32} height={32} className="sm:w-10 sm:h-10" />
        </div>

        {/* Search */}
        <div className="flex-1 px-2 sm:px-6 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
          <input
            type="text"
            placeholder="Search..."
            className="w-full px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2 sm:gap-4 relative flex-shrink-0">
          {/* Country Selector */}
          <div className="w-20 sm:w-[130px] border-none">
            <Select
              options={countryOptions}
              value={selectedCountry}
              onChange={(val) => setSelectedCountry(val)}
              isSearchable={false}
              className="text-xs sm:text-sm"
              formatOptionLabel={(option) => (
                <div className="flex items-center gap-1 sm:gap-2">
                  <img
                    src={option.flag}
                    alt={option.label}
                    style={{
                      width: '16px',
                      height: '12px',
                      objectFit: 'cover',
                      borderRadius: '2px',
                    }}
                    className="sm:w-5 sm:h-4"
                  />
                  <span className="text-xs hidden sm:inline">{option.label}</span>
                </div>
              )}
                styles={{
             control: (base) => ({
  ...base,
  borderRadius: '999px',
  paddingLeft: '4px',
  minHeight: '28px',
  boxShadow: 'none',
  border: 'none',
  fontSize: '12px',
}),

                indicatorsContainer: (base) => ({
                  ...base,
                  paddingRight: '4px',
                }),
              }}
            />
          </div>

          {/* Cart Icon */}
          <button
            onClick={() => setShowCart(!showCart)}
            className="text-gray-500 hover:text-blue-600 text-lg sm:text-xl relative bg-transparent border-none outline-none p-1"
          >
            <FaShoppingCart />
          </button>

          {/* Mini Cart Dropdown */}
          {showCart && (
            <div className="absolute top-[45px] sm:top-[50px] right-0 bg-white shadow-lg rounded-lg w-[250px] sm:w-[300px] z-50">
              <div className="p-3 sm:p-4">
                <h3 className="text-xs sm:text-sm font-semibold mb-2">Shopping Cart</h3>
                <div className="border-t pt-2">
                  <div className="flex justify-between items-center mb-2">
                    <div>
                      <p className="text-xs sm:text-sm font-medium">Sample Product</p>
                      <p className="text-xs text-gray-500">Qty: 1</p>
                    </div>
                    <span className="text-xs sm:text-sm">$10</span>
                  </div>
                </div>
                <Link
                  href="/cart"
                  className="block text-center mt-4 bg-blue-500 text-white py-1.5 sm:py-2 text-xs sm:text-sm rounded hover:bg-blue-600 transition"
                >
                  Go to Cart
                </Link>
              </div>
            </div>
          )}

          {/* User Authentication */}
          {user ? (
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 bg-green-500 text-white text-xs sm:text-sm font-medium px-3 sm:px-4 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-green-600 transition-all duration-300"
              >
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-white rounded-full flex items-center justify-center">
                  <FaUserCircle className="text-green-500 text-sm sm:text-lg" />
                </div>
                <span className="hidden sm:inline">{user.userName || user.name || 'المستخدم'}</span>
              </button>

              {showUserMenu && (
                <div className="absolute top-[45px] sm:top-[50px] right-0 bg-white shadow-lg rounded-lg w-[220px] sm:w-[280px] z-50 border">
                  <div className="p-3 sm:p-4">
                    {/* User Info Header */}
                    <div className="border-b pb-3 mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <FaUserCircle className="text-green-500 text-xl" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800">{user.userName || user.name}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="space-y-1">
                      <Link
                        href="/profile"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaUser className="text-blue-500" />
                        My Profile
                      </Link>

                      <Link
                        href="/my-listings"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaList className="text-purple-500" />
                        My Listing
                      </Link>

                      <Link
                        href="/wallet"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaWallet className="text-green-500" />
                        My Wallet
                      </Link>

                      <Link
                        href="/messages"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaEnvelope className="text-orange-500" />
                        Messages
                      </Link>

                      <Link
                        href="/settings"
                        className="flex items-center gap-3 w-full text-left text-sm text-gray-700 hover:bg-gray-50 p-2 rounded transition"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaCog className="text-gray-500" />
                        Settings
                      </Link>

                      {/* Divider */}
                      <div className="border-t my-2"></div>

                      <button
                        onClick={handleLogout}
                        className="flex items-center gap-3 w-full text-left text-sm text-red-600 hover:bg-red-50 p-2 rounded transition"
                      >
                        <FaSignOutAlt className="text-red-500" />
                        Log Out
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Link
                href="/signup"
                className="inline-block bg-gray-500 text-white text-xs sm:text-sm font-medium px-2 sm:px-4 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-gray-600 transition-all duration-300 whitespace-nowrap border-0"
                style={{ textDecoration: 'none' }}
              >
                📝 سجل الآن
              </Link>
              <Link
                href="/login"
                className="inline-block bg-blue-500 text-white text-xs sm:text-sm font-medium px-3 sm:px-6 py-1.5 sm:py-2 rounded-full shadow-sm hover:bg-blue-600 hover:shadow-md transition-all duration-300 whitespace-nowrap border-0"
                style={{
                  textDecoration: 'none',
                  outline: 'none',
                  boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'
                }}
              >
                🔐 دخول
              </Link>
            </div>
          )}

        </div>
      </div>
    </div>
  );
}


