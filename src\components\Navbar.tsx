import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';
import './NavbarStyles.css'; // ملف CSS مخصص

function ResponsiveNavbar() {
  return (
    <>
      <Navbar expand="lg" bg="white" className="shadow-sm">
        <Container>
          <Navbar.Brand href="#home" className="fw-bold text-primary">
            🛒 Buy1
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              <Nav.Link href="#home" className="nav-link-custom">Home</Nav.Link>
              <Nav.Link href="#buy-accounts" className="nav-link-custom">Buy Accounts</Nav.Link>
              <Nav.Link href="#sell-accounts" className="nav-link-custom">Sell Accounts</Nav.Link>
              <Nav.Link href="#top-up" className="nav-link-custom">Top-Up</Nav.Link>
              <Nav.Link href="#marketplace" className="nav-link-custom">Marketplace</Nav.Link>
              <Nav.Link href="#offers-deals" className="nav-link-custom">Offers & Deals</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}

export default ResponsiveNavbar;
