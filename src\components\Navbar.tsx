import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';



function ResponsiveNavbar() {
  return (
    <>
      {/* ✅ No Brand - Only Navigation Links */}
      <Navbar expand="lg" bg="white" className="shadow-sm">
        <Container>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="mx-auto">
              <Nav.Link href="#home" className="nav-link-custom mx-3 hover:text-blue-600">Home</Nav.Link>
              <Nav.Link href="#buy-accounts" className="nav-link-custom mx-3 hover:text-blue-600 ">Buy Accounts</Nav.Link>
              <Nav.Link href="#sell-accounts" className="nav-link-custom mx-3 hover:text-blue-600">Sell Accounts</Nav.Link>
              <Nav.Link href="#sell-accounts" className="nav-link-custom mx-3 hover:text-blue-600">Sell Accounts</Nav.Link>
              <Nav.Link href="#top-up" className="nav-link-custom mx-3  hover:text-blue-600">Top-Up</Nav.Link>
              <Nav.Link href="#marketplace" className="nav-link-custom mx-3  hover:text-blue-600">Marketplace</Nav.Link>
              <Nav.Link href="#offers-deals" className="nav-link-custom mx-3 hover:text-blue-600">Offers & Deals</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}

export default ResponsiveNavbar;
