'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/context/UserContext';
import { walletAPI } from '@/services/api';
import { FaWallet, FaPlus, FaHistory, FaMoneyBillWave } from 'react-icons/fa';

export default function WalletPage() {
  const { user } = useUser();
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showRecharge, setShowRecharge] = useState(false);
  const [rechargeData, setRechargeData] = useState({
    country: 'EG',
    telecom: 'Vodafone',
    amount: '',
    phoneNumber: ''
  });
  const [message, setMessage] = useState('');

  const countries = [
    { code: 'EG', name: 'Egypt' },
    { code: 'SA', name: 'Saudi Arabia' },
    { code: 'A<PERSON>', name: 'UAE' }
  ];

  const telecoms = {
    EG: ['Vodafone', 'Orange', 'Etisalat', 'WE'],
    SA: ['STC', 'Mobily', 'Zain'],
    AE: ['Etisalat', 'du']
  };

  useEffect(() => {
    if (user) {
      fetchWalletData();
    }
  }, [user]);

  const fetchWalletData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch balance
      const balanceData = await walletAPI.getBalance();
      setBalance(balanceData.balance || 0);
      
      // Fetch transactions
      const transactionsData = await walletAPI.getTransactions();
      setTransactions(transactionsData.transactions || []);
      
    } catch (err) {
      console.error('Error fetching wallet data:', err);
      setMessage('خطأ في تحميل بيانات المحفظة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecharge = async (e) => {
    e.preventDefault();
    
    if (!rechargeData.amount || !rechargeData.phoneNumber) {
      setMessage('يرجى ملء جميع الحقول');
      return;
    }

    try {
      setIsLoading(true);
      setMessage('');
      
      await walletAPI.rechargeRequest({
        country: rechargeData.country,
        telecom: rechargeData.telecom,
        amount: parseInt(rechargeData.amount),
        phoneNumber: rechargeData.phoneNumber
      });
      
      setMessage('تم إرسال طلب الشحن بنجاح! 🎉');
      setShowRecharge(false);
      setRechargeData({
        country: 'EG',
        telecom: 'Vodafone',
        amount: '',
        phoneNumber: ''
      });
      
      // Refresh wallet data
      fetchWalletData();
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ غير متوقع';
      setMessage(`خطأ: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">يرجى تسجيل الدخول</h2>
          <p className="text-gray-600">تحتاج إلى تسجيل الدخول لعرض محفظتك</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FaWallet className="text-green-500" />
            My Wallet
          </h1>
        </div>

        {/* Balance Card */}
        <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg shadow-lg p-6 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">الرصيد الحالي</p>
              <p className="text-3xl font-bold">
                {isLoading ? '...' : `$${balance.toFixed(2)}`}
              </p>
            </div>
            <div className="text-right">
              <FaMoneyBillWave className="text-4xl text-green-200" />
            </div>
          </div>
          
          <div className="mt-4">
            <button
              onClick={() => setShowRecharge(true)}
              className="flex items-center gap-2 bg-white text-green-600 px-4 py-2 rounded-md hover:bg-gray-100 transition font-medium"
            >
              <FaPlus />
              شحن المحفظة
            </button>
          </div>
        </div>

        {/* Recharge Modal */}
        {showRecharge && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-xl font-bold mb-4">شحن المحفظة</h3>
              
              <form onSubmit={handleRecharge} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الدولة</label>
                  <select
                    value={rechargeData.country}
                    onChange={(e) => setRechargeData({
                      ...rechargeData,
                      country: e.target.value,
                      telecom: telecoms[e.target.value][0]
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    {countries.map(country => (
                      <option key={country.code} value={country.code}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">شركة الاتصالات</label>
                  <select
                    value={rechargeData.telecom}
                    onChange={(e) => setRechargeData({ ...rechargeData, telecom: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    {telecoms[rechargeData.country]?.map(telecom => (
                      <option key={telecom} value={telecom}>
                        {telecom}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ</label>
                  <input
                    type="number"
                    value={rechargeData.amount}
                    onChange={(e) => setRechargeData({ ...rechargeData, amount: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="أدخل المبلغ"
                    min="1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                  <input
                    type="tel"
                    value={rechargeData.phoneNumber}
                    onChange={(e) => setRechargeData({ ...rechargeData, phoneNumber: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="أدخل رقم الهاتف"
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 bg-green-500 text-white py-2 rounded-md hover:bg-green-600 transition disabled:opacity-50"
                  >
                    {isLoading ? 'جاري الإرسال...' : 'شحن'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowRecharge(false)}
                    className="flex-1 bg-gray-500 text-white py-2 rounded-md hover:bg-gray-600 transition"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Transactions */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <FaHistory className="text-blue-500" />
              سجل المعاملات
            </h2>
          </div>
          
          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : transactions.length === 0 ? (
              <div className="text-center py-8">
                <FaHistory className="text-gray-300 text-4xl mx-auto mb-4" />
                <p className="text-gray-600">لا توجد معاملات حتى الآن</p>
              </div>
            ) : (
              <div className="space-y-4">
                {transactions.map((transaction, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{transaction.type || 'معاملة'}</p>
                      <p className="text-sm text-gray-600">{transaction.date || 'تاريخ غير محدد'}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.amount > 0 ? '+' : ''}${transaction.amount}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className={`mt-4 p-3 rounded-md text-sm ${
            message.includes('نجاح') || message.includes('🎉')
              ? 'bg-green-100 text-green-700'
              : 'bg-red-100 text-red-700'
          }`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
}
