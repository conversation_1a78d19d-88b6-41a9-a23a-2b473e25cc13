// API Services for Game Top-up Application
const API_BASE_URL = 'http://localhost:3000';

// Helper function to get auth token
const getAuthToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('authToken');
  }
  return null;
};

// Helper function to create headers
const createHeaders = (includeAuth = false) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `e_${token}`;
    }
  }
  
  return headers;
};

// Auth API calls
export const authAPI = {
  // Sign Up
  signUp: async (userData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(userData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Sign up failed');
      }
      
      return data;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  },

  // Login
  login: async (credentials) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(credentials),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }
      
      // Store token in localStorage
      if (data.token && typeof window !== 'undefined') {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('userData', JSON.stringify(data.user || data));
      }
      
      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Forget Password
  forgetPassword: async (email) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/forget`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Forget password failed');
      }
      
      return data;
    } catch (error) {
      console.error('Forget password error:', error);
      throw error;
    }
  },

  // Reset Password
  resetPassword: async (resetData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/reset`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(resetData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Reset password failed');
      }
      
      return data;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  },

  // Logout
  logout: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!getAuthToken();
  },

  // Get current user data
  getCurrentUser: () => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    }
    return null;
  }
};

// User API calls
export const userAPI = {
  // Change Password
  changePassword: async (userId, passwordData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/user/changePassword/${userId}`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify(passwordData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Change password failed');
      }
      
      return data;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  },

  // Logout (API call)
  logout: async (userId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/user/logout`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify({ userId }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Logout failed');
      }
      
      // Clear local storage
      authAPI.logout();
      
      return data;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  },

  // Edit Profile
  editProfile: async (userId, profileData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/user/editProfile/${userId}`, {
        method: 'PATCH',
        headers: createHeaders(true),
        body: JSON.stringify(profileData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Edit profile failed');
      }
      
      return data;
    } catch (error) {
      console.error('Edit profile error:', error);
      throw error;
    }
  },

  // Get User by ID
  getUserById: async (userId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/user/getuserById/${userId}`, {
        method: 'GET',
        headers: createHeaders(true),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Get user failed');
      }
      
      return data;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }
};

// Order API calls
export const orderAPI = {
  // Get Orders
  getOrders: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/order`, {
        method: 'GET',
        headers: createHeaders(true),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Get orders failed');
      }
      
      return data;
    } catch (error) {
      console.error('Get orders error:', error);
      throw error;
    }
  },

  // Create Order
  createOrder: async (orderData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify(orderData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Create order failed');
      }
      
      return data;
    } catch (error) {
      console.error('Create order error:', error);
      throw error;
    }
  }
};

// Wallet API calls
export const walletAPI = {
  // Get Balance
  getBalance: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/balance`, {
        method: 'GET',
        headers: createHeaders(true),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Get balance failed');
      }
      
      return data;
    } catch (error) {
      console.error('Get balance error:', error);
      throw error;
    }
  },

  // Recharge Request
  rechargeRequest: async (rechargeData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/rechargerequest`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify(rechargeData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Recharge request failed');
      }
      
      return data;
    } catch (error) {
      console.error('Recharge request error:', error);
      throw error;
    }
  },

  // Get Transactions
  getTransactions: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/transactions`, {
        method: 'GET',
        headers: createHeaders(true),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Get transactions failed');
      }
      
      return data;
    } catch (error) {
      console.error('Get transactions error:', error);
      throw error;
    }
  }
};
